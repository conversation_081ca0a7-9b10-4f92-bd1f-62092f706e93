# Shopify Checkout UI Extensions - Демонстрация всех Extension Targets

Этот проект содержит примеры всех основных extension targets из Shopify Checkout UI Extensions API (2025-10-rc).

## 📋 Созданные Extension Targets

### 🧩 Block Extensions
- **block-checkout.tsx** - `purchase.checkout.block.render`
  - Блочное расширение для чекаута, позиционируется через редактор
- **block-thankyou.tsx** - `purchase.thank-you.block.render`
  - Блочное расширение для страницы "Спасибо"

### 📄 Header Extensions
- **header-checkout.tsx** - `purchase.checkout.header.render-after`
  - Расширение под заголовком чекаута
- **header-thankyou.tsx** - `purchase.thank-you.header.render-after`
  - Расширение под заголовком страницы "Спасибо"

### 🦶 Footer Extensions
- **footer-checkout.tsx** - `purchase.checkout.footer.render-after`
  - Расширение под футером чекаута
- **footer-thankyou.tsx** - `purchase.thank-you.footer.render-after`
  - Расширение под футером страницы "Спасибо"

### ℹ️ Information Extensions
- **contact-info.tsx** - `purchase.checkout.contact.render-after`
  - Расширение после формы контактной информации
- **customer-info-thankyou.tsx** - `purchase.thank-you.customer-information.render-after`
  - Расширение после информации о клиенте на странице "Спасибо"

### 🚚 Shipping Extensions
- **delivery-address.tsx** - `purchase.checkout.delivery-address.render-after`
  - Расширение после формы адреса доставки
- **delivery-address-before.tsx** - `purchase.checkout.delivery-address.render-before`
  - Расширение перед формой адреса доставки
- **shipping-options-before.tsx** - `purchase.checkout.shipping-option-list.render-before`
  - Расширение перед списком способов доставки
- **shipping-options-after.tsx** - `purchase.checkout.shipping-option-list.render-after`
  - Расширение после списка способов доставки

### 🛒 Order Summary Extensions
- **cart-line-item.tsx** - `purchase.checkout.cart-line-item.render-after`
  - Расширение для каждого товара в корзине
- **cart-line-list.tsx** - `purchase.checkout.cart-line-list.render-after`
  - Расширение после списка товаров
- **reductions-before.tsx** - `purchase.checkout.reductions.render-before`
  - Расширение перед формой скидок
- **reductions-after.tsx** - `purchase.checkout.reductions.render-after`
  - Расширение после формы скидок

### 💳 Payment Extensions
- **payment-methods-before.tsx** - `purchase.checkout.payment-method-list.render-before`
  - Расширение перед списком способов оплаты
- **payment-methods-after.tsx** - `purchase.checkout.payment-method-list.render-after`
  - Расширение после списка способов оплаты

### 🧭 Navigation Extensions
- **actions-before.tsx** - `purchase.checkout.actions.render-before`
  - Расширение перед кнопками действий

### 📍 Pickup Extensions
- **pickup-locations-before.tsx** - `purchase.checkout.pickup-location-list.render-before`
  - Расширение перед списком пунктов самовывоза
- **pickup-points-before.tsx** - `purchase.checkout.pickup-point-list.render-before`
  - Расширение перед списком пунктов выдачи

## 🚀 Как запустить

1. Установите зависимости:
```bash
npm install
```

2. Запустите сервер разработки:
```bash
npm run dev
```

3. Откройте URL, который покажет Shopify CLI

## 📖 Документация

Все extension targets основаны на официальной документации Shopify:
- [Extension Targets Overview](https://shopify.dev/docs/api/checkout-ui-extensions/2025-10-rc/extension-targets-overview)
- [All Extension Targets](https://shopify.dev/docs/api/checkout-ui-extensions/2025-10-rc/targets)

## 🎯 Цель проекта

Этот проект создан для демонстрации и тестирования всех доступных extension targets в Shopify Checkout UI Extensions. Каждый компонент показывает:

- Где именно отображается расширение
- Какие возможности доступны в данной позиции
- Примеры использования UI компонентов
- Интеграцию с Checkout API

## 📝 Примечания

- Все компоненты содержат отладочную информацию с названием target
- Используются различные UI компоненты для демонстрации возможностей
- Некоторые расширения содержат интерактивные элементы
- Проект использует API версии 2025-07 (latest stable)

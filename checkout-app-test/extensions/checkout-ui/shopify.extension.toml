# Learn more about configuring your checkout UI extension:
# https://shopify.dev/docs/api/checkout-ui-extensions/latest/configuration

# The version of APIs your extension will receive. Learn more:
# https://shopify.dev/docs/api/usage/versioning
api_version = "2025-07"

[[extensions]]
name = "checkout-ui"
handle = "checkout-ui"
type = "ui_extension"
uid = "fddfc370-27c7-c30f-4ee0-a927194e2accadefd40c"

# Controls where in Shopify your extension will be injected,
# and the file that contains your extension’s source code. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/latest/extension-targets-overview

# Block Extensions
[[extensions.targeting]]
module = "./src/block-checkout.tsx"
target = "purchase.checkout.block.render"

[[extensions.targeting]]
module = "./src/block-thankyou.tsx"
target = "purchase.thank-you.block.render"

# Header Extensions
[[extensions.targeting]]
module = "./src/header-checkout.tsx"
target = "purchase.checkout.header.render-after"

[[extensions.targeting]]
module = "./src/header-thankyou.tsx"
target = "purchase.thank-you.header.render-after"

# Footer Extensions
[[extensions.targeting]]
module = "./src/footer-checkout.tsx"
target = "purchase.checkout.footer.render-after"

[[extensions.targeting]]
module = "./src/footer-thankyou.tsx"
target = "purchase.thank-you.footer.render-after"

# Information Extensions
[[extensions.targeting]]
module = "./src/contact-info.tsx"
target = "purchase.checkout.contact.render-after"

[[extensions.targeting]]
module = "./src/customer-info-thankyou.tsx"
target = "purchase.thank-you.customer-information.render-after"

# Shipping Extensions
[[extensions.targeting]]
module = "./src/delivery-address.tsx"
target = "purchase.checkout.delivery-address.render-after"

[[extensions.targeting]]
module = "./src/delivery-address-before.tsx"
target = "purchase.checkout.delivery-address.render-before"

[[extensions.targeting]]
module = "./src/shipping-options-before.tsx"
target = "purchase.checkout.shipping-option-list.render-before"

[[extensions.targeting]]
module = "./src/shipping-options-after.tsx"
target = "purchase.checkout.shipping-option-list.render-after"

# Order Summary Extensions
[[extensions.targeting]]
module = "./src/cart-line-item.tsx"
target = "purchase.checkout.cart-line-item.render-after"

[[extensions.targeting]]
module = "./src/cart-line-list.tsx"
target = "purchase.checkout.cart-line-list.render-after"

[[extensions.targeting]]
module = "./src/reductions-before.tsx"
target = "purchase.checkout.reductions.render-before"

[[extensions.targeting]]
module = "./src/reductions-after.tsx"
target = "purchase.checkout.reductions.render-after"

# Payment Extensions
[[extensions.targeting]]
module = "./src/payment-methods-before.tsx"
target = "purchase.checkout.payment-method-list.render-before"

[[extensions.targeting]]
module = "./src/payment-methods-after.tsx"
target = "purchase.checkout.payment-method-list.render-after"

# Navigation Extensions
[[extensions.targeting]]
module = "./src/actions-before.tsx"
target = "purchase.checkout.actions.render-before"

# Pickup Extensions
[[extensions.targeting]]
module = "./src/pickup-locations-before.tsx"
target = "purchase.checkout.pickup-location-list.render-before"

[[extensions.targeting]]
module = "./src/pickup-points-before.tsx"
target = "purchase.checkout.pickup-point-list.render-before"

# Original Extensions (keeping for reference - commented out to avoid duplicates)
# [[extensions.targeting]]
# module = "./src/Checkout.tsx"
# target = "purchase.checkout.block.render"

# [[extensions.targeting]]
# module = "./src/ch-test.tsx"
# target = "purchase.checkout.block.render"

[extensions.capabilities]
# Gives your extension access to directly query Shopify’s storefront API.
# https://shopify.dev/docs/api/checkout-ui-extensions/latest/configuration#api-access
api_access = true

# Gives your extension access to make external network calls, using the
# JavaScript `fetch()` API. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/latest/configuration#network-access
# network_access = true

# Loads metafields on checkout resources, including the cart,
# products, customers, and more. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/latest/configuration#metafields

# [[extensions.metafields]]
# namespace = "my_namespace"
# key = "my_key"
# [[extensions.metafields]]
# namespace = "my_namespace"
# key = "my_other_key"

# Defines settings that will be collected from merchants installing
# your extension. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/latest/configuration#settings-definition

# [extensions.settings]
# [[extensions.settings.fields]]
# key = "banner_title"
# type = "single_line_text_field"
# name = "Banner title"
# description = "Enter a title for the banner"

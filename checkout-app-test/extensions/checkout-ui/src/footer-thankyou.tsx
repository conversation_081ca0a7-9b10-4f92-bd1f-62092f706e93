import {
  reactExtension,
  BlockStack,
  Text,
  useApi,
} from "@shopify/ui-extensions-react/checkout";

// Footer extension target for Thank You page
export default reactExtension("purchase.thank-you.footer.render-after", () => (
  <ThankYouFooterExtension />
));

function ThankYouFooterExtension() {
  const { extension } = useApi();

  return (
    <BlockStack padding="base" border="base">
      <Text size="small" appearance="subdued" alignment="center">
        Thank You Footer Extension - {extension.target}
      </Text>
      <Text size="small" alignment="center">
        Спасибо за покупку! Ваш заказ обрабатывается.
      </Text>
    </BlockStack>
  );
}

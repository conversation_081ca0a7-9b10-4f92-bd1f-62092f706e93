import {
  reactExtension,
  BlockStack,
  Text,
  Checkbox,
  TextField,
  useApi,
  useApplyAttributeChange,
} from "@shopify/ui-extensions-react/checkout";
import { useState } from "react";

// Delivery address after extension target
export default reactExtension("purchase.checkout.delivery-address.render-after", () => (
  <DeliveryAddressExtension />
));

function DeliveryAddressExtension() {
  const { extension } = useApi();
  const applyAttributeChange = useApplyAttributeChange();

  // Состояние для дополнительных полей
  const [test1Value, setTest1Value] = useState("");
  const [test2Value, setTest2Value] = useState("");

  const handleSpecialInstructions = async (isChecked: boolean) => {
    await applyAttributeChange({
      key: "special_delivery_instructions",
      type: "updateAttribute",
      value: isChecked ? "yes" : "no",
    });
  };

  const handleTest1Change = async (value: string) => {
    setTest1Value(value);
    await applyAttributeChange({
      key: "billing_test_field_1",
      type: "updateAttribute",
      value: value,
    });
  };

  const handleTest2Change = async (value: string) => {
    setTest2Value(value);
    await applyAttributeChange({
      key: "billing_test_field_2",
      type: "updateAttribute",
      value: value,
    });
  };

  return (
    <BlockStack spacing="base" padding="base" border="base" cornerRadius="base">
      <Text emphasis="bold">Дополнительная информация для адреса</Text>

      <Checkbox onChange={handleSpecialInstructions}>
        <Text>Добавить специальные инструкции для доставки</Text>
      </Checkbox>

      <Text size="small" appearance="subdued">
        Например: "Оставить у двери", "Позвонить перед доставкой"
      </Text>

      <BlockStack spacing="tight">
        <TextField
          label="Тест 1"
          value={test1Value}
          onChange={handleTest1Change}
        />

        <TextField
          label="Тест 2"
          value={test2Value}
          onChange={handleTest2Change}
        />
      </BlockStack>

      <Text size="small" appearance="subdued">
        💡 Дополнительные поля помогут нам лучше обработать ваш заказ
      </Text>
    </BlockStack>
  );
}

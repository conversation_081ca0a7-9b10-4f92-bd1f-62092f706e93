import {
  reactExtension,
  BlockStack,
  Text,
  Checkbox,
  useApi,
  useApplyAttributeChange,
} from "@shopify/ui-extensions-react/checkout";

// Delivery address after extension target
export default reactExtension("purchase.checkout.delivery-address.render-after", () => (
  <DeliveryAddressExtension />
));

function DeliveryAddressExtension() {
  const { extension } = useApi();
  const applyAttributeChange = useApplyAttributeChange();

  const handleSpecialInstructions = async (isChecked: boolean) => {
    await applyAttributeChange({
      key: "special_delivery_instructions",
      type: "updateAttribute",
      value: isChecked ? "yes" : "no",
    });
  };

  return (
    <BlockStack spacing="tight" padding="base" border="base">
      <Text size="small" appearance="subdued">
        Delivery Address After - {extension.target}
      </Text>

      <Checkbox onChange={handleSpecialInstructions}>
        <Text>Добавить специальные инструкции для доставки</Text>
      </Checkbox>

      <Text size="small" appearance="subdued">
        Например: "Оставить у двери", "Позвонить перед доставкой"
      </Text>
    </BlockStack>
  );
}

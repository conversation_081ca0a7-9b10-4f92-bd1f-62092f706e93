# Security Policy

## Supported versions

### New features

New features will only be added to the master branch and will not be made available in point releases.

### Bug fixes

Only the latest release series will receive bug fixes. When enough bugs are fixed and its deemed worthy to release a new gem, this is the branch it happens from.

### Security issues

Only the latest release series will receive patches and new versions in case of a security issue.

### Severe security issues

For severe security issues we will provide new versions as above, and also the last major release series will receive patches and new versions. The classification of the security issue is judged by the core team.

### Unsupported Release Series

When a release series is no longer supported, it's your own responsibility to deal with bugs and security issues. If you are not comfortable maintaining your own versions, you should upgrade to a supported version.

## Reporting a bug

All security bugs in shopify repositories should be reported to [our hackerone program](https://hackerone.com/shopify)
Shopify's whitehat program is our way to reward security researchers for finding serious security vulnerabilities in the In Scope properties listed at the bottom of this page, including our core application (all functionality associated with a Shopify store, particularly your-store.myshopify.com/admin) and certain ancillary applications.

## Disclosure Policy

We look forward to working with all security researchers and strive to be respectful, always assume the best and treat others as peers. We expect the same in return from all participants. To achieve this, our team strives to:

- Reply to all reports within one business day and triage within two business days (if applicable)
- Be as transparent as possible, answering all inquires about our report decisions and adding hackers to duplicate HackerOne reports
- Award bounties within a week of resolution (excluding extenuating circumstances)
- Only close reports as N/A when the issue reported is included in Known Issues, Ineligible Vulnerabilities Types or lacks evidence of a vulnerability

**The following rules must be followed in order for any rewards to be paid:**

- You may only test against shops you have created which include your HackerOne YOURHANDLE @ wearehackerone.com registered email address.
- You must not attempt to gain access to, or interact with, any shops other than those created by you.
- The use of commercial scanners is prohibited (e.g., Nessus).
- Rules for reporting must be followed.
- Do not disclose any issues publicly before they have been resolved.
- Shopify reserves the right to modify the rules for this program or deem any submissions invalid at any time. Shopify may cancel the whitehat program without notice at any time.
- Contacting Shopify Support over chat, email or phone about your HackerOne report is not allowed. We may disqualify you from receiving a reward, or from participating in the program altogether.
- You are not an employee of Shopify; employees should report bugs to the internal bug bounty program.
- You hereby represent, warrant and covenant that any content you submit to Shopify is an original work of authorship and that you are legally entitled to grant the rights and privileges conveyed by these terms. You further represent, warrant and covenant that the consent of no other person or entity is or will be necessary for Shopify to use the submitted content.
- By submitting content to Shopify, you irrevocably waive all moral rights which you may have in the content.
- All content submitted by you to Shopify under this program is licensed under the MIT License.
- You must report any discovered vulnerability to Shopify as soon as you have validated the vulnerability.
- Failure to follow any of the foregoing rules will disqualify you from participating in this program.

\*\* Please see our [Hackerone Profile](https://hackerone.com/shopify) for full details

## Receiving Security Updates

To receive all general updates to vulnerabilities, please subscribe to our hackerone [Hacktivity](https://hackerone.com/shopify/hacktivity)
